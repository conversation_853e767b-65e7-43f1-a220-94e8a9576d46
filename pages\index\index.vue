<template>
	<view class="container">
		<!-- 头部横幅 -->
		<view class="header-banner">
			<image class="banner-bg" src="/static/header-bg.jpg" mode="aspectFill"></image>
			<view class="banner-overlay">
				<view class="banner-content">
					<text class="banner-title">玉泉区人力资源和社会保障局</text>
					<text class="banner-subtitle">为民服务 · 创新发展</text>
				</view>
			</view>
		</view>

		<!-- 快捷服务 -->
		<view class="quick-services">
			<view class="service-title">便民服务</view>
			<view class="service-grid">
				<view class="service-item" v-for="item in services" :key="item.id" @click="openService(item)">
					<text class="service-icon">{{ item.icon }}</text>
					<text class="service-name">{{ item.name }}</text>
				</view>
			</view>
		</view>

		<!-- 最新公告 -->
		<view class="latest-notices">
			<view class="section-header">
				<text class="section-title">最新公告</text>
				<text class="section-more" @click="goToNotices">更多 ></text>
			</view>
			<view class="notice-list">
				<view class="notice-item" v-for="item in latestNotices" :key="item.id" @click="viewNotice(item)">
					<view class="notice-content">
						<text class="notice-title">{{ item.title }}</text>
						<text class="notice-date">{{ formatDate(item.publishDate) }}</text>
					</view>
					<text class="notice-tag" :class="'tag-' + item.category">{{ getCategoryLabel(item.category) }}</text>
				</view>
			</view>
		</view>

		<!-- 热门政策 -->
		<view class="hot-policies">
			<view class="section-header">
				<text class="section-title">热门政策</text>
				<text class="section-more" @click="goToPolicies">更多 ></text>
			</view>
			<view class="policy-list">
				<view class="policy-item" v-for="item in hotPolicies" :key="item.id" @click="viewPolicy(item)">
					<image class="policy-thumb" :src="item.thumbnail" mode="aspectFill"></image>
					<view class="policy-content">
						<text class="policy-title">{{ item.title }}</text>
						<text class="policy-summary">{{ item.summary }}</text>
						<view class="policy-meta">
							<text class="policy-date">{{ formatDate(item.publishDate) }}</text>
							<text class="policy-views">{{ item.views }}次阅读</text>
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 联系我们 -->
		<view class="contact-info">
			<view class="contact-title">联系我们</view>
			<view class="contact-item">
				<text class="contact-label">服务热线：</text>
				<text class="contact-value" @click="makeCall">0471-12333</text>
			</view>
			<view class="contact-item">
				<text class="contact-label">办公地址：</text>
				<text class="contact-value">呼和浩特市玉泉区...</text>
			</view>
			<view class="contact-item">
				<text class="contact-label">办公时间：</text>
				<text class="contact-value">周一至周五 9:00-17:00</text>
			</view>
		</view>
	</view>
</template>

<script setup>
import { ref } from 'vue'

// 响应式数据
const services = ref([
	{ id: 1, name: '就业服务', icon: '💼' },
	{ id: 2, name: '社保查询', icon: '🛡️' },
	{ id: 3, name: '政策咨询', icon: '📋' },
	{ id: 4, name: '在线办事', icon: '💻' },
	{ id: 5, name: '投诉建议', icon: '📝' },
	{ id: 6, name: '联系我们', icon: '📞' }
])

const latestNotices = ref([
	{
		id: 1,
		title: '玉泉区2024年事业单位公开招聘工作人员公告',
		category: 'recruitment',
		publishDate: '2024-01-15'
	},
	{
		id: 2,
		title: '关于调整最低工资标准的通知',
		category: 'policy',
		publishDate: '2024-01-12'
	},
	{
		id: 3,
		title: '失业保险金申领办事指南',
		category: 'guide',
		publishDate: '2024-01-10'
	}
])

const hotPolicies = ref([
	{
		id: 1,
		title: '创业担保贷款政策',
		summary: '为促进创业带动就业，加大创业扶持力度...',
		publishDate: '2024-01-15',
		views: 2156,
		thumbnail: '/static/policy1.jpg'
	},
	{
		id: 2,
		title: '失业保险稳岗返还',
		summary: '为减轻企业负担，稳定就业岗位...',
		publishDate: '2024-01-12',
		views: 1892,
		thumbnail: '/static/policy2.jpg'
	}
])

const categories = ref([
	{ label: '招聘公告', value: 'recruitment' },
	{ label: '政策解读', value: 'policy' },
	{ label: '办事指南', value: 'guide' },
	{ label: '通知公告', value: 'notice' }
])

// 方法
const openService = (item) => {
	uni.showToast({
		title: `${item.name}功能待开发`,
		icon: 'none'
	})
}

const goToNotices = () => {
	uni.switchTab({
		url: '/pages/notice/notice'
	})
}

const goToPolicies = () => {
	uni.switchTab({
		url: '/pages/policy/policy'
	})
}

const viewNotice = (item) => {
	uni.navigateTo({
		url: `/pages/notice/detail?id=${item.id}`
	})
}

const viewPolicy = (item) => {
	uni.showToast({
		title: '政策详情功能待开发',
		icon: 'none'
	})
}

const makeCall = () => {
	uni.makePhoneCall({
		phoneNumber: '0471-12333'
	})
}

const getCategoryLabel = (category) => {
	const item = categories.value.find(cat => cat.value === category)
	return item ? item.label : ''
}

const formatDate = (dateStr) => {
	const date = new Date(dateStr)
	return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`
}
</script>

<style scoped>
.container {
	background-color: #f5f5f5;
	min-height: 100vh;
}

.header-banner {
	height: 400rpx;
	position: relative;
	overflow: hidden;
}

.banner-bg {
	width: 100%;
	height: 100%;
}

.banner-overlay {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: linear-gradient(135deg, rgba(25, 118, 210, 0.8), rgba(25, 118, 210, 0.6));
	display: flex;
	align-items: center;
	justify-content: center;
}

.banner-content {
	text-align: center;
	color: #fff;
}

.banner-title {
	font-size: 40rpx;
	font-weight: bold;
	margin-bottom: 20rpx;
	display: block;
}

.banner-subtitle {
	font-size: 28rpx;
	opacity: 0.9;
}

.quick-services {
	background-color: #fff;
	padding: 30rpx;
	margin-bottom: 20rpx;
}

.service-title {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 30rpx;
}

.service-grid {
	display: flex;
	flex-wrap: wrap;
	justify-content: space-between;
}

.service-item {
	width: 30%;
	text-align: center;
	padding: 30rpx 20rpx;
	background-color: #f8f9fa;
	border-radius: 12rpx;
	margin-bottom: 20rpx;
}

.service-icon {
	font-size: 48rpx;
	display: block;
	margin-bottom: 15rpx;
}

.service-name {
	font-size: 26rpx;
	color: #333;
}

.latest-notices, .hot-policies {
	background-color: #fff;
	margin-bottom: 20rpx;
}

.section-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 30rpx;
	border-bottom: 1rpx solid #eee;
}

.section-title {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
}

.section-more {
	font-size: 28rpx;
	color: #1976D2;
}

.notice-list {
	padding: 0 30rpx;
}

.notice-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 25rpx 0;
	border-bottom: 1rpx solid #eee;
}

.notice-item:last-child {
	border-bottom: none;
}

.notice-content {
	flex: 1;
	margin-right: 20rpx;
}

.notice-title {
	font-size: 30rpx;
	color: #333;
	margin-bottom: 8rpx;
	display: block;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.notice-date {
	font-size: 24rpx;
	color: #999;
}

.notice-tag {
	padding: 8rpx 16rpx;
	border-radius: 20rpx;
	font-size: 22rpx;
	color: #fff;
}

.tag-recruitment { background-color: #FF5722; }
.tag-policy { background-color: #4CAF50; }
.tag-guide { background-color: #FF9800; }
.tag-notice { background-color: #2196F3; }

.policy-list {
	padding: 0 30rpx;
}

.policy-item {
	display: flex;
	padding: 30rpx 0;
	border-bottom: 1rpx solid #eee;
}

.policy-item:last-child {
	border-bottom: none;
}

.policy-thumb {
	width: 160rpx;
	height: 120rpx;
	border-radius: 8rpx;
	margin-right: 20rpx;
}

.policy-content {
	flex: 1;
}

.policy-title {
	font-size: 30rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 15rpx;
	display: block;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.policy-summary {
	font-size: 26rpx;
	color: #666;
	line-height: 1.4;
	margin-bottom: 15rpx;
	display: -webkit-box;
	-webkit-line-clamp: 2;
	-webkit-box-orient: vertical;
	overflow: hidden;
}

.policy-meta {
	display: flex;
	justify-content: space-between;
}

.policy-date, .policy-views {
	font-size: 24rpx;
	color: #999;
}

.contact-info {
	background-color: #fff;
	padding: 30rpx;
	margin-bottom: 20rpx;
}

.contact-title {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 30rpx;
}

.contact-item {
	display: flex;
	margin-bottom: 20rpx;
}

.contact-item:last-child {
	margin-bottom: 0;
}

.contact-label {
	font-size: 28rpx;
	color: #666;
	width: 180rpx;
}

.contact-value {
	font-size: 28rpx;
	color: #333;
	flex: 1;
}
</style>
