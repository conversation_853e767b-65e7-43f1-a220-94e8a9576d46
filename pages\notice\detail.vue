<template>
	<view class="container">
		<!-- 加载状态 -->
		<view class="loading" v-if="loading">
			<text class="loading-text">加载中...</text>
		</view>
		
		<!-- 公告详情 -->
		<view class="notice-detail" v-else-if="noticeDetail">
			<!-- 头部信息 -->
			<view class="notice-header">
				<text class="notice-title">{{ noticeDetail.title }}</text>
				<view class="notice-meta">
					<text class="notice-tag" :class="'tag-' + noticeDetail.category">
						{{ getCategoryLabel(noticeDetail.category) }}
					</text>
					<text class="notice-date">{{ formatDate(noticeDetail.publishDate) }}</text>
				</view>
				<view class="notice-info">
					<text class="info-item">发布单位：{{ noticeDetail.publisher }}</text>
					<text class="info-item">阅读量：{{ noticeDetail.views }}次</text>
				</view>
			</view>
			
			<!-- 正文内容 -->
			<view class="notice-content">
				<rich-text class="content-text" :nodes="noticeDetail.content"></rich-text>
			</view>
			
			<!-- 附件下载 -->
			<view class="attachments" v-if="noticeDetail.attachments && noticeDetail.attachments.length > 0">
				<view class="section-title">相关附件</view>
				<view class="attachment-list">
					<view class="attachment-item" 
						  v-for="attachment in noticeDetail.attachments" 
						  :key="attachment.id"
						  @click="downloadAttachment(attachment)">
						<text class="attachment-icon">📎</text>
						<view class="attachment-info">
							<text class="attachment-name">{{ attachment.name }}</text>
							<text class="attachment-size">{{ attachment.size }}</text>
						</view>
						<text class="download-icon">⬇️</text>
					</view>
				</view>
			</view>
			
			<!-- 相关公告 -->
			<view class="related-notices" v-if="relatedNotices.length > 0">
				<view class="section-title">相关公告</view>
				<view class="related-list">
					<view class="related-item" 
						  v-for="item in relatedNotices" 
						  :key="item.id"
						  @click="viewRelated(item)">
						<text class="related-title">{{ item.title }}</text>
						<text class="related-date">{{ formatDate(item.publishDate) }}</text>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 错误状态 -->
		<view class="error" v-else>
			<text class="error-text">公告不存在或已删除</text>
			<button class="back-btn" @click="goBack">返回列表</button>
		</view>
		
		<!-- 底部操作栏 -->
		<view class="bottom-actions" v-if="noticeDetail">
			<button class="action-btn share-btn" @click="shareNotice">
				<text class="btn-icon">📤</text>
				<text class="btn-text">分享</text>
			</button>
			<button class="action-btn collect-btn" @click="toggleCollect">
				<text class="btn-icon">{{ isCollected ? '❤️' : '🤍' }}</text>
				<text class="btn-text">{{ isCollected ? '已收藏' : '收藏' }}</text>
			</button>
			<button class="action-btn back-btn" @click="goBack">
				<text class="btn-icon">↩️</text>
				<text class="btn-text">返回</text>
			</button>
		</view>
	</view>
</template>

<script setup>
import { ref, onLoad } from 'vue'

// 响应式数据
const loading = ref(true)
const noticeDetail = ref(null)
const isCollected = ref(false)
const relatedNotices = ref([])

const categories = ref([
	{ label: '招聘公告', value: 'recruitment' },
	{ label: '政策解读', value: 'policy' },
	{ label: '办事指南', value: 'guide' },
	{ label: '通知公告', value: 'notice' }
])

// 模拟公告详情数据
const mockNoticeDetails = {
	1: {
		id: 1,
		title: '玉泉区2024年事业单位公开招聘工作人员公告',
		category: 'recruitment',
		publishDate: '2024-01-15',
		publisher: '玉泉区人力资源和社会保障局',
		views: 1256,
		content: `
			<p>根据《事业单位人事管理条例》、《事业单位公开招聘人员暂行规定》等相关规定，经玉泉区委、区政府研究决定，面向社会公开招聘事业单位工作人员。现将有关事项公告如下：</p>
			
			<h3>一、招聘原则</h3>
			<p>坚持德才兼备、以德为先的用人标准，贯彻公开、平等、竞争、择优的原则，实行公开招聘，在考试、考察的基础上择优聘用。</p>
			
			<h3>二、招聘岗位及人数</h3>
			<p>本次共招聘事业单位工作人员50名，具体岗位及要求详见《玉泉区2024年事业单位公开招聘工作人员岗位表》。</p>
			
			<h3>三、招聘条件</h3>
			<p>1. 具有中华人民共和国国籍；</p>
			<p>2. 遵守宪法和法律；</p>
			<p>3. 具有良好的品行；</p>
			<p>4. 具有正常履行职责的身体条件；</p>
			<p>5. 具有符合岗位要求的工作能力；</p>
			<p>6. 年龄在18周岁以上、35周岁以下（1988年1月15日至2006年1月15日期间出生）；</p>
			<p>7. 具备岗位所需的其他条件。</p>
			
			<h3>四、报名时间及方式</h3>
			<p>报名时间：2024年1月20日9:00至1月25日17:00</p>
			<p>报名方式：网上报名，登录玉泉区人民政府官网进行报名。</p>
			
			<h3>五、联系方式</h3>
			<p>咨询电话：0471-12333</p>
			<p>监督电话：0471-12345</p>
		`,
		attachments: [
			{
				id: 1,
				name: '玉泉区2024年事业单位公开招聘工作人员岗位表.xlsx',
				size: '156KB'
			},
			{
				id: 2,
				name: '报名表.doc',
				size: '45KB'
			}
		]
	},
	2: {
		id: 2,
		title: '关于调整最低工资标准的通知',
		category: 'policy',
		publishDate: '2024-01-12',
		publisher: '玉泉区人力资源和社会保障局',
		views: 892,
		content: `
			<p>根据自治区人民政府关于调整全区最低工资标准的通知精神，现将玉泉区最低工资标准调整情况公布如下：</p>
			
			<h3>一、调整标准</h3>
			<p>自2024年2月1日起，玉泉区最低工资标准调整为：</p>
			<p>月最低工资标准：1980元/月</p>
			<p>小时最低工资标准：19.8元/小时</p>
			
			<h3>二、适用范围</h3>
			<p>本标准适用于玉泉区行政区域内的企业、民办非企业单位、有雇工的个体工商户等用人单位。</p>
			
			<h3>三、执行要求</h3>
			<p>各用人单位要严格执行最低工资标准，不得违反相关规定。</p>
		`,
		attachments: []
	}
}

// 页面加载
onLoad((options) => {
	const noticeId = options.id
	if (noticeId) {
		loadNoticeDetail(noticeId)
	} else {
		loading.value = false
	}
})

// 方法
const loadNoticeDetail = async (id) => {
	try {
		loading.value = true
		
		// 模拟API请求延迟
		await new Promise(resolve => setTimeout(resolve, 1000))
		
		// 获取公告详情
		const detail = mockNoticeDetails[id]
		if (detail) {
			noticeDetail.value = detail
			// 增加阅读量
			detail.views += 1
			
			// 加载相关公告
			loadRelatedNotices(detail.category, detail.id)
		}
	} catch (error) {
		console.error('加载公告详情失败:', error)
	} finally {
		loading.value = false
	}
}

const loadRelatedNotices = (category, currentId) => {
	// 模拟相关公告数据
	const allNotices = [
		{ id: 3, title: '失业保险金申领办事指南', category: 'guide', publishDate: '2024-01-10' },
		{ id: 4, title: '玉泉区人社局春节放假通知', category: 'notice', publishDate: '2024-01-08' },
		{ id: 5, title: '2024年职业技能培训补贴申请指南', category: 'guide', publishDate: '2024-01-05' }
	]
	
	relatedNotices.value = allNotices.filter(item => 
		item.category === category && item.id !== currentId
	).slice(0, 3)
}

const getCategoryLabel = (category) => {
	const item = categories.value.find(cat => cat.value === category)
	return item ? item.label : ''
}

const formatDate = (dateStr) => {
	const date = new Date(dateStr)
	return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`
}

const downloadAttachment = (attachment) => {
	uni.showToast({
		title: `下载 ${attachment.name}`,
		icon: 'none'
	})
}

const shareNotice = () => {
	uni.share({
		provider: 'weixin',
		type: 0,
		title: noticeDetail.value.title,
		summary: '来自玉泉区人社局的重要公告',
		success: () => {
			uni.showToast({
				title: '分享成功',
				icon: 'success'
			})
		},
		fail: () => {
			uni.showToast({
				title: '分享功能待开发',
				icon: 'none'
			})
		}
	})
}

const toggleCollect = () => {
	isCollected.value = !isCollected.value
	uni.showToast({
		title: isCollected.value ? '收藏成功' : '取消收藏',
		icon: 'success'
	})
}

const viewRelated = (item) => {
	uni.navigateTo({
		url: `/pages/notice/detail?id=${item.id}`
	})
}

const goBack = () => {
	uni.navigateBack()
}
</script>

<style scoped>
.container {
	background-color: #f5f5f5;
	min-height: 100vh;
	padding-bottom: 120rpx;
}

.loading {
	display: flex;
	justify-content: center;
	align-items: center;
	height: 400rpx;
}

.loading-text {
	font-size: 28rpx;
	color: #999;
}

.notice-detail {
	background-color: #fff;
	margin: 20rpx;
	border-radius: 12rpx;
	overflow: hidden;
}

.notice-header {
	padding: 40rpx 30rpx 30rpx;
	border-bottom: 1rpx solid #eee;
}

.notice-title {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
	line-height: 1.4;
	margin-bottom: 20rpx;
	display: block;
}

.notice-meta {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 20rpx;
}

.notice-tag {
	padding: 8rpx 16rpx;
	border-radius: 20rpx;
	font-size: 22rpx;
	color: #fff;
}

.tag-recruitment { background-color: #FF5722; }
.tag-policy { background-color: #4CAF50; }
.tag-guide { background-color: #FF9800; }
.tag-notice { background-color: #2196F3; }

.notice-date {
	font-size: 26rpx;
	color: #999;
}

.notice-info {
	display: flex;
	justify-content: space-between;
}

.info-item {
	font-size: 24rpx;
	color: #666;
}

.notice-content {
	padding: 30rpx;
}

.content-text {
	font-size: 30rpx;
	line-height: 1.6;
	color: #333;
}

.content-text h3 {
	font-size: 32rpx;
	font-weight: bold;
	color: #1976D2;
	margin: 30rpx 0 20rpx;
}

.content-text p {
	margin-bottom: 20rpx;
}

.attachments, .related-notices {
	margin: 20rpx;
	background-color: #fff;
	border-radius: 12rpx;
	padding: 30rpx;
}

.section-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 20rpx;
	display: block;
}

.attachment-list {
	/* 附件列表样式 */
}

.attachment-item {
	display: flex;
	align-items: center;
	padding: 20rpx 0;
	border-bottom: 1rpx solid #eee;
}

.attachment-item:last-child {
	border-bottom: none;
}

.attachment-icon {
	font-size: 32rpx;
	margin-right: 20rpx;
}

.attachment-info {
	flex: 1;
}

.attachment-name {
	font-size: 28rpx;
	color: #333;
	display: block;
	margin-bottom: 8rpx;
}

.attachment-size {
	font-size: 24rpx;
	color: #999;
}

.download-icon {
	font-size: 28rpx;
	color: #1976D2;
}

.related-list {
	/* 相关公告列表样式 */
}

.related-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 20rpx 0;
	border-bottom: 1rpx solid #eee;
}

.related-item:last-child {
	border-bottom: none;
}

.related-title {
	flex: 1;
	font-size: 28rpx;
	color: #333;
	margin-right: 20rpx;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.related-date {
	font-size: 24rpx;
	color: #999;
}

.error {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	height: 400rpx;
	padding: 40rpx;
}

.error-text {
	font-size: 28rpx;
	color: #999;
	margin-bottom: 30rpx;
}

.bottom-actions {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	background-color: #fff;
	border-top: 1rpx solid #eee;
	display: flex;
	padding: 20rpx;
	box-shadow: 0 -2rpx 8rpx rgba(0,0,0,0.1);
}

.action-btn {
	flex: 1;
	display: flex;
	flex-direction: column;
	align-items: center;
	background: none;
	border: none;
	padding: 15rpx;
	margin: 0 10rpx;
	border-radius: 8rpx;
}

.share-btn {
	background-color: #f8f9fa;
}

.collect-btn {
	background-color: #f8f9fa;
}

.back-btn {
	background-color: #1976D2;
	color: #fff;
}

.btn-icon {
	font-size: 32rpx;
	margin-bottom: 8rpx;
}

.btn-text {
	font-size: 24rpx;
}

.back-btn .btn-text {
	color: #fff;
}
</style>
