<template>
	<view class="container">
		<!-- 轮播图 -->
		<swiper class="banner-swiper" indicator-dots="true" autoplay="true" interval="3000" duration="500">
			<swiper-item v-for="item in banners" :key="item.id">
				<image class="banner-image" :src="item.image" mode="aspectFill" @click="viewBanner(item)"></image>
				<view class="banner-overlay">
					<text class="banner-title">{{ item.title }}</text>
				</view>
			</swiper-item>
		</swiper>
		
		<!-- 政策分类 -->
		<view class="policy-categories">
			<view class="category-title">政策分类</view>
			<view class="category-grid">
				<view class="category-card" v-for="item in policyCategories" :key="item.id" @click="viewCategory(item)">
					<text class="category-icon">{{ item.icon }}</text>
					<text class="category-name">{{ item.name }}</text>
					<text class="category-count">{{ item.count }}项</text>
				</view>
			</view>
		</view>
		
		<!-- 热门政策 -->
		<view class="hot-policies">
			<view class="section-header">
				<text class="section-title">热门政策</text>
				<text class="section-more" @click="viewMore('hot')">更多 ></text>
			</view>
			<view class="policy-list">
				<view class="policy-item" v-for="item in hotPolicies" :key="item.id" @click="viewPolicy(item)">
					<view class="policy-content">
						<text class="policy-title">{{ item.title }}</text>
						<text class="policy-summary">{{ item.summary }}</text>
						<view class="policy-meta">
							<text class="policy-date">{{ formatDate(item.publishDate) }}</text>
							<text class="policy-views">{{ item.views }}次阅读</text>
						</view>
					</view>
					<image class="policy-thumb" :src="item.thumbnail" mode="aspectFill"></image>
				</view>
			</view>
		</view>
		
		<!-- 最新政策 -->
		<view class="latest-policies">
			<view class="section-header">
				<text class="section-title">最新政策</text>
				<text class="section-more" @click="viewMore('latest')">更多 ></text>
			</view>
			<view class="policy-simple-list">
				<view class="policy-simple-item" v-for="item in latestPolicies" :key="item.id" @click="viewPolicy(item)">
					<text class="policy-simple-title">{{ item.title }}</text>
					<text class="policy-simple-date">{{ formatDate(item.publishDate) }}</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script setup>
import { ref } from 'vue'

// 响应式数据
const banners = ref([
	{
		id: 1,
		title: '2024年就业创业扶持政策解读',
		image: '/static/banner1.jpg'
	},
	{
		id: 2,
		title: '社会保险政策宣传',
		image: '/static/banner2.jpg'
	},
	{
		id: 3,
		title: '人才引进优惠政策',
		image: '/static/banner3.jpg'
	}
])

const policyCategories = ref([
	{ id: 1, name: '就业创业', icon: '💼', count: 15 },
	{ id: 2, name: '社会保险', icon: '🛡️', count: 12 },
	{ id: 3, name: '人才政策', icon: '👥', count: 8 },
	{ id: 4, name: '劳动关系', icon: '🤝', count: 10 },
	{ id: 5, name: '技能培训', icon: '📚', count: 6 },
	{ id: 6, name: '工资福利', icon: '💰', count: 9 }
])

const hotPolicies = ref([
	{
		id: 1,
		title: '玉泉区创业担保贷款政策实施细则',
		summary: '为进一步促进创业带动就业，加大创业扶持力度，根据相关规定制定本实施细则...',
		publishDate: '2024-01-15',
		views: 2156,
		thumbnail: '/static/policy1.jpg'
	},
	{
		id: 2,
		title: '失业保险稳岗返还政策解读',
		summary: '为减轻企业负担，稳定就业岗位，对符合条件的企业实施失业保险稳岗返还政策...',
		publishDate: '2024-01-12',
		views: 1892,
		thumbnail: '/static/policy2.jpg'
	},
	{
		id: 3,
		title: '高校毕业生就业创业补贴政策',
		summary: '为促进高校毕业生充分就业，对符合条件的毕业生给予一次性就业创业补贴...',
		publishDate: '2024-01-10',
		views: 1654,
		thumbnail: '/static/policy3.jpg'
	}
])

const latestPolicies = ref([
	{
		id: 4,
		title: '关于调整工伤保险待遇标准的通知',
		publishDate: '2024-01-18'
	},
	{
		id: 5,
		title: '职业技能提升行动实施方案',
		publishDate: '2024-01-16'
	},
	{
		id: 6,
		title: '灵活就业人员社保缴费指南',
		publishDate: '2024-01-14'
	},
	{
		id: 7,
		title: '企业职工基本养老保险政策解读',
		publishDate: '2024-01-11'
	},
	{
		id: 8,
		title: '劳动争议调解仲裁办法',
		publishDate: '2024-01-09'
	}
])

// 方法
const viewBanner = (item) => {
	uni.showToast({
		title: '轮播图详情功能待开发',
		icon: 'none'
	})
}

const viewCategory = (item) => {
	uni.showToast({
		title: `查看${item.name}分类`,
		icon: 'none'
	})
}

const viewPolicy = (item) => {
	uni.showToast({
		title: '政策详情功能待开发',
		icon: 'none'
	})
}

const viewMore = (type) => {
	uni.showToast({
		title: `查看更多${type === 'hot' ? '热门' : '最新'}政策`,
		icon: 'none'
	})
}

const formatDate = (dateStr) => {
	const date = new Date(dateStr)
	return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`
}
</script>

<style scoped>
.container {
	background-color: #f5f5f5;
	min-height: 100vh;
}

.banner-swiper {
	height: 400rpx;
	position: relative;
}

.banner-image {
	width: 100%;
	height: 100%;
}

.banner-overlay {
	position: absolute;
	bottom: 0;
	left: 0;
	right: 0;
	background: linear-gradient(transparent, rgba(0,0,0,0.6));
	padding: 40rpx 30rpx 30rpx;
}

.banner-title {
	color: #fff;
	font-size: 32rpx;
	font-weight: bold;
}

.policy-categories {
	background-color: #fff;
	padding: 30rpx;
	margin-bottom: 20rpx;
}

.category-title {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 30rpx;
}

.category-grid {
	display: flex;
	flex-wrap: wrap;
	justify-content: space-between;
}

.category-card {
	width: 30%;
	background-color: #f8f9fa;
	border-radius: 12rpx;
	padding: 30rpx 20rpx;
	text-align: center;
	margin-bottom: 20rpx;
}

.category-icon {
	font-size: 48rpx;
	display: block;
	margin-bottom: 15rpx;
}

.category-name {
	font-size: 28rpx;
	color: #333;
	display: block;
	margin-bottom: 8rpx;
}

.category-count {
	font-size: 24rpx;
	color: #999;
}

.hot-policies, .latest-policies {
	background-color: #fff;
	margin-bottom: 20rpx;
}

.section-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 30rpx;
	border-bottom: 1rpx solid #eee;
}

.section-title {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
}

.section-more {
	font-size: 28rpx;
	color: #1976D2;
}

.policy-list {
	padding: 0 30rpx;
}

.policy-item {
	display: flex;
	padding: 30rpx 0;
	border-bottom: 1rpx solid #eee;
}

.policy-item:last-child {
	border-bottom: none;
}

.policy-content {
	flex: 1;
	margin-right: 20rpx;
}

.policy-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	line-height: 1.4;
	margin-bottom: 15rpx;
	display: block;
}

.policy-summary {
	font-size: 28rpx;
	color: #666;
	line-height: 1.5;
	margin-bottom: 15rpx;
	display: -webkit-box;
	-webkit-line-clamp: 2;
	-webkit-box-orient: vertical;
	overflow: hidden;
}

.policy-meta {
	display: flex;
	justify-content: space-between;
}

.policy-date, .policy-views {
	font-size: 24rpx;
	color: #999;
}

.policy-thumb {
	width: 200rpx;
	height: 150rpx;
	border-radius: 8rpx;
}

.policy-simple-list {
	padding: 0 30rpx;
}

.policy-simple-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 25rpx 0;
	border-bottom: 1rpx solid #eee;
}

.policy-simple-item:last-child {
	border-bottom: none;
}

.policy-simple-title {
	flex: 1;
	font-size: 30rpx;
	color: #333;
	margin-right: 20rpx;
}

.policy-simple-date {
	font-size: 26rpx;
	color: #999;
}
</style>
