<script>
	export default {
		onLaunch: function() {
			console.log('玉泉区人社局官网启动')
		},
		onShow: function() {
			console.log('App Show')
		},
		onHide: function() {
			console.log('App Hide')
		}
	}
</script>

<style>
	/* 全局样式 */
	page {
		background-color: #f5f5f5;
		font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
	}

	/* 通用容器样式 */
	.container {
		background-color: #f5f5f5;
		min-height: 100vh;
	}

	/* 通用卡片样式 */
	.card {
		background-color: #fff;
		border-radius: 12rpx;
		box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
		margin-bottom: 20rpx;
	}

	/* 通用按钮样式 */
	.btn-primary {
		background-color: #1976D2;
		color: #fff;
		border-radius: 8rpx;
		padding: 20rpx 40rpx;
		font-size: 28rpx;
		text-align: center;
	}

	.btn-secondary {
		background-color: #f8f9fa;
		color: #333;
		border: 1rpx solid #dee2e6;
		border-radius: 8rpx;
		padding: 20rpx 40rpx;
		font-size: 28rpx;
		text-align: center;
	}

	/* 通用文本样式 */
	.text-primary { color: #1976D2; }
	.text-secondary { color: #666; }
	.text-muted { color: #999; }
	.text-success { color: #4CAF50; }
	.text-warning { color: #FF9800; }
	.text-danger { color: #F44336; }

	/* 通用间距样式 */
	.mt-10 { margin-top: 10rpx; }
	.mt-20 { margin-top: 20rpx; }
	.mt-30 { margin-top: 30rpx; }
	.mb-10 { margin-bottom: 10rpx; }
	.mb-20 { margin-bottom: 20rpx; }
	.mb-30 { margin-bottom: 30rpx; }
	.p-20 { padding: 20rpx; }
	.p-30 { padding: 30rpx; }

	/* 通用布局样式 */
	.flex { display: flex; }
	.flex-column { flex-direction: column; }
	.flex-center { justify-content: center; align-items: center; }
	.flex-between { justify-content: space-between; }
	.flex-around { justify-content: space-around; }
	.flex-1 { flex: 1; }

	/* 通用文本省略 */
	.text-ellipsis {
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}

	.text-ellipsis-2 {
		display: -webkit-box;
		-webkit-line-clamp: 2;
		-webkit-box-orient: vertical;
		overflow: hidden;
	}
</style>
