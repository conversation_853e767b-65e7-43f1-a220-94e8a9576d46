<template>
	<view class="container">
		<!-- 搜索栏 -->
		<view class="search-bar">
			<input class="search-input" placeholder="搜索公示公告..." v-model="searchKeyword" @input="onSearch" />
			<text class="search-icon">🔍</text>
		</view>
		
		<!-- 分类筛选 -->
		<view class="category-filter">
			<scroll-view class="category-scroll" scroll-x="true">
				<view class="category-item" 
					  :class="{ active: selectedCategory === item.value }"
					  v-for="item in categories" 
					  :key="item.value"
					  @click="selectCategory(item.value)">
					{{ item.label }}
				</view>
			</scroll-view>
		</view>
		
		<!-- 公告列表 -->
		<view class="notice-list">
			<view class="notice-item" v-for="item in filteredNotices" :key="item.id" @click="viewDetail(item)">
				<view class="notice-header">
					<text class="notice-title">{{ item.title }}</text>
					<text class="notice-tag" :class="'tag-' + item.category">{{ getCategory<PERSON>abel(item.category) }}</text>
				</view>
				<view class="notice-content">
					<text class="notice-summary">{{ item.summary }}</text>
				</view>
				<view class="notice-footer">
					<text class="notice-date">{{ formatDate(item.publishDate) }}</text>
					<text class="notice-views">阅读 {{ item.views }}</text>
				</view>
			</view>
		</view>
		
		<!-- 加载更多 -->
		<view class="load-more" v-if="hasMore" @click="loadMore">
			<text class="load-more-text">加载更多</text>
		</view>
		
		<!-- 暂无数据 -->
		<view class="no-data" v-if="filteredNotices.length === 0">
			<text class="no-data-text">暂无相关公告</text>
		</view>
	</view>
</template>

<script setup>
import { ref, computed } from 'vue'

// 响应式数据
const searchKeyword = ref('')
const selectedCategory = ref('all')
const hasMore = ref(true)

const categories = ref([
	{ label: '全部', value: 'all' },
	{ label: '招聘公告', value: 'recruitment' },
	{ label: '政策解读', value: 'policy' },
	{ label: '办事指南', value: 'guide' },
	{ label: '通知公告', value: 'notice' }
])

const notices = ref([
	{
		id: 1,
		title: '玉泉区2024年事业单位公开招聘工作人员公告',
		summary: '根据《事业单位人事管理条例》等相关规定，经玉泉区委、区政府研究决定，面向社会公开招聘事业单位工作人员...',
		category: 'recruitment',
		publishDate: '2024-01-15',
		views: 1256
	},
	{
		id: 2,
		title: '关于调整最低工资标准的通知',
		summary: '根据自治区人民政府关于调整全区最低工资标准的通知精神，现将玉泉区最低工资标准调整情况公布如下...',
		category: 'policy',
		publishDate: '2024-01-12',
		views: 892
	},
	{
		id: 3,
		title: '失业保险金申领办事指南',
		summary: '为方便失业人员及时了解和申领失业保险金，现将申领条件、所需材料、办理流程等相关事项说明如下...',
		category: 'guide',
		publishDate: '2024-01-10',
		views: 654
	},
	{
		id: 4,
		title: '玉泉区人社局春节放假通知',
		summary: '根据国务院办公厅关于2024年部分节假日安排的通知，现将春节期间我局放假安排及相关工作事项通知如下...',
		category: 'notice',
		publishDate: '2024-01-08',
		views: 432
	}
])

// 计算属性
const filteredNotices = computed(() => {
	let result = notices.value

	// 按分类筛选
	if (selectedCategory.value !== 'all') {
		result = result.filter(item => item.category === selectedCategory.value)
	}

	// 按关键词搜索
	if (searchKeyword.value.trim()) {
		const keyword = searchKeyword.value.trim().toLowerCase()
		result = result.filter(item =>
			item.title.toLowerCase().includes(keyword) ||
			item.summary.toLowerCase().includes(keyword)
		)
	}

	return result
})

// 方法
const onSearch = () => {
	// 搜索逻辑已在computed中处理
}

const selectCategory = (category) => {
	selectedCategory.value = category
}

const getCategoryLabel = (category) => {
	const item = categories.value.find(cat => cat.value === category)
	return item ? item.label : ''
}

const formatDate = (dateStr) => {
	const date = new Date(dateStr)
	return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`
}

const viewDetail = (item) => {
	uni.navigateTo({
		url: `/pages/notice/detail?id=${item.id}`
	})
}

const loadMore = () => {
	uni.showToast({
		title: '加载更多功能待开发',
		icon: 'none'
	})
}
</script>

<style scoped>
.container {
	background-color: #f5f5f5;
	min-height: 100vh;
}

.search-bar {
	background-color: #fff;
	padding: 20rpx;
	display: flex;
	align-items: center;
	position: relative;
}

.search-input {
	flex: 1;
	height: 70rpx;
	background-color: #f8f8f8;
	border-radius: 35rpx;
	padding: 0 40rpx 0 70rpx;
	font-size: 28rpx;
}

.search-icon {
	position: absolute;
	left: 40rpx;
	font-size: 32rpx;
	color: #999;
}

.category-filter {
	background-color: #fff;
	padding: 20rpx 0;
	border-bottom: 1rpx solid #eee;
}

.category-scroll {
	white-space: nowrap;
}

.category-item {
	display: inline-block;
	padding: 12rpx 24rpx;
	margin: 0 10rpx;
	background-color: #f8f8f8;
	border-radius: 30rpx;
	font-size: 26rpx;
	color: #666;
}

.category-item.active {
	background-color: #1976D2;
	color: #fff;
}

.notice-list {
	padding: 20rpx;
}

.notice-item {
	background-color: #fff;
	border-radius: 12rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.notice-header {
	display: flex;
	justify-content: space-between;
	align-items: flex-start;
	margin-bottom: 20rpx;
}

.notice-title {
	flex: 1;
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	line-height: 1.4;
	margin-right: 20rpx;
}

.notice-tag {
	padding: 8rpx 16rpx;
	border-radius: 20rpx;
	font-size: 22rpx;
	color: #fff;
}

.tag-recruitment { background-color: #FF5722; }
.tag-policy { background-color: #4CAF50; }
.tag-guide { background-color: #FF9800; }
.tag-notice { background-color: #2196F3; }

.notice-content {
	margin-bottom: 20rpx;
}

.notice-summary {
	font-size: 28rpx;
	color: #666;
	line-height: 1.5;
	display: -webkit-box;
	-webkit-line-clamp: 2;
	-webkit-box-orient: vertical;
	overflow: hidden;
}

.notice-footer {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.notice-date, .notice-views {
	font-size: 24rpx;
	color: #999;
}

.load-more {
	text-align: center;
	padding: 40rpx;
}

.load-more-text {
	color: #1976D2;
	font-size: 28rpx;
}

.no-data {
	text-align: center;
	padding: 100rpx 40rpx;
}

.no-data-text {
	color: #999;
	font-size: 28rpx;
}
</style>
